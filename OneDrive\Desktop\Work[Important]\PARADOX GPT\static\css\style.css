/* ParadoxGPT - Minimalistic Black & White Theme */
:root {
    /* Clean Monochrome Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f5f5f5;
    --bg-sidebar: #ffffff;
    --bg-input: #ffffff;
    --bg-message-user: #f8f9fa;
    --bg-message-assistant: #ffffff;
    --bg-code: #f8f9fa;
    --bg-modal: rgba(0, 0, 0, 0.5);
    --bg-hover: #f5f5f5;

    /* Text Colors */
    --text-primary: #000000;
    --text-secondary: #333333;
    --text-tertiary: #666666;
    --text-quaternary: #999999;
    --text-inverse: #ffffff;
    --text-code: #000000;
    --text-muted: #888888;

    /* Minimal Accent */
    --accent-primary: #000000;
    --accent-secondary: #333333;
    --accent-hover: #666666;
    --accent-error: #dc3545;
    --accent-success: #28a745;
    --border-color: #e0e0e0;
    --border-hover: #cccccc;
    --border-focus: #000000;

    /* Clean Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius - Minimal */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;

    /* Smooth Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;

    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Layout */
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 0px;
    --input-max-height: 120px;
    --toggle-button-size: 40px;
}

/* Simple Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    height: 100%;
}

body {
    font-family: var(--font-family);
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow: hidden;
    height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}



/* App Container */
.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

/* Minimal Sidebar Toggle Button */
.sidebar-toggle {
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    width: var(--toggle-button-size);
    height: var(--toggle-button-size);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow-sm);
    
}

.sidebar-toggle:hover {
    background: var(--bg-hover);
    border-color: var(--border-hover);
}

.sidebar-toggle:active {
    background: var(--bg-tertiary);
}

/* Toggle button visibility controlled by JavaScript */

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 100vw;
        max-width: 320px;
    }

    .sidebar:not(.collapsed) ~ .main-content {
        margin-left: 0;
        width: 100%;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .welcome-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

/* Minimal Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-sidebar);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: transform var(--transition-normal);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    transform: translateX(0);
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.new-chat-btn {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 2px black solid;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.new-chat-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-hover);
}

.new-chat-btn:active {
    background: var(--bg-tertiary);
}

.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.chat-history::-webkit-scrollbar {
    width: 6px;
}

.chat-history::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.chat-history::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-full);
}

.chat-history::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: var(--accent-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: 0.875rem;
    font-weight: 600;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-left: 0;
    transition: margin-left var(--transition-normal);
}

.sidebar:not(.collapsed) ~ .main-content {
    margin-left: var(--sidebar-width);
}

/* Chat Container */
.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    scroll-behavior: smooth;
}

.chat-container::-webkit-scrollbar {
    width: 8px;
}

.chat-container::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.chat-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-full);
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* Welcome Section */
.welcome-section {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
    padding: var(--spacing-2xl);
}

.welcome-content {
    text-align: center;
    max-width: 500px;
}

.welcome-logo {
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
    font-size: 3rem;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.welcome-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.5;
    font-weight: 400;
}

/* Message Styles */
.message {
    padding: var(--spacing-lg);
    margin: var(--spacing-md) var(--spacing-lg);
    display: flex;
    gap: var(--spacing-md);
    max-width: none;
    transition: all var(--transition-normal);
    animation: slideUp 0.3s ease-out;
}

.message.user {
    background: var(--bg-message-user);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-left: auto;
    margin-right: var(--spacing-lg);
    max-width: 50%;
}

.message.assistant {
    background: var(--bg-message-assistant);
    margin-left: var(--spacing-lg);
    margin-right: auto;
    max-width: 80%;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 0.875rem;
    font-weight: 600;
}

.message.user .message-avatar {
    background: var(--accent-primary);
    color: var(--text-inverse);
}

.message.assistant .message-avatar {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.message-content {
    flex: 1;
    min-width: 0;
    position: relative;
}

.message-content p {
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
    color: var(--text-primary);
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.message-content ul,
.message-content ol {
    margin-bottom: var(--spacing-lg);
    padding-left: var(--spacing-xl);
}

.message-content li {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.message-footer {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    opacity: 0.8;
}

.message-footer small {
    color: var(--text-tertiary);
    font-size: 0.8rem;
    font-weight: 500;
}

.message-actions {
    display: flex;
    gap: var(--spacing-md);
}

.message-action-btn {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    color: var(--text-tertiary);
    font-size: 0.875rem;
    cursor: pointer;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.message-action-btn:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-hover);
    color: var(--accent-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Futuristic Code Blocks */
.message-content pre {
    background: var(--bg-code);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin: var(--spacing-lg) 0;
    position: relative;
    box-shadow: var(--shadow-lg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

.message-content pre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary));
    z-index: 1;
}

.message-content code {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-code);
    font-weight: 500;
}

.message-content p code {
    background: var(--bg-glass);
    color: var(--accent-primary);
    padding: 0.2rem 0.4rem;
    border-radius: var(--radius-sm);
    font-size: 0.85rem;
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
}

.code-block-container {
    position: relative;
    margin: var(--spacing-lg) 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.code-block-container pre {
    padding: var(--spacing-xl);
    margin: 0;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--glass-border) transparent;
}

.code-block-container pre::-webkit-scrollbar {
    height: 6px;
}

.code-block-container pre::-webkit-scrollbar-track {
    background: transparent;
}

.code-block-container pre::-webkit-scrollbar-thumb {
    background: var(--glass-border);
    border-radius: var(--radius-full);
}

.code-controls {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    display: flex;
    gap: var(--spacing-sm);
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: 10;
}

.code-block-container:hover .code-controls {
    opacity: 1;
}

.code-controls button {
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop-strong);
    -webkit-backdrop-filter: var(--glass-backdrop-strong);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-md);
}

.code-controls button:hover {
    background: var(--bg-glass-hover);
    border-color: var(--glass-border-hover);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg), var(--glow-soft);
}

.preview-button {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary)) !important;
    color: var(--text-inverse) !important;
    border-color: var(--accent-primary) !important;
    box-shadow: var(--shadow-lg), var(--glow-primary) !important;
}

.preview-button:hover {
    background: linear-gradient(135deg, var(--accent-secondary), var(--accent-primary)) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: var(--shadow-xl), var(--glow-primary) !important;
}

/* Input Section */
.input-section {
    flex-shrink: 0;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-primary);
}

.input-container {
    max-width: 500px;
    margin: 0 auto;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-sm);
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    transition: all var(--transition-normal);
}

.input-wrapper:focus-within {
    border-color: var(--border-focus);
}

.message-input {
    flex: 1;
    background: none;
    border: 1px solid transparent;
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: 0.875rem;
    line-height: 3.5;
    resize: none;
    outline: none !important;
    max-height: var(--input-max-height);
    min-height: 20px;
    overflow-y: auto;
    padding: 0;
}

.message-input::placeholder {
    color: var(--text-tertiary);
}

.message-input::-webkit-scrollbar {
    width: 4px;
}

.message-input::-webkit-scrollbar-track {
    background: transparent;
}

.message-input::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--radius-full);
}

.send-btn {
    background: var(--accent-primary);
    border: none;
    color: var(--text-inverse);
    cursor: pointer;
    padding: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    width: 36px;
    height: 36px;
    transition: all var(--transition-normal);
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: var(--accent-hover);
}

.send-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-quaternary);
    cursor: not-allowed;
}

.input-footer {
    margin-top: var(--spacing-sm);
    text-align: center;
}

.disclaimer {
    font-size: 0.75rem;
    color: var(--text-quaternary);
    line-height: 1.4;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
}

.typing-dots {
    display: flex;
    gap: var(--spacing-xs);
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: var(--text-tertiary);
    border-radius: var(--radius-full);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Futuristic Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-modal);
    backdrop-filter: var(--glass-backdrop-strong);
    -webkit-backdrop-filter: var(--glass-backdrop-strong);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
    background: var(--bg-glass);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-2xl), var(--glow-primary);
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-xl);
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-radius: var(--radius-full);
    animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
    border-top: 4px solid var(--accent-primary);
    animation-duration: 2s;
}

.spinner-ring:nth-child(2) {
    border-right: 4px solid var(--accent-secondary);
    animation-duration: 1.5s;
    animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
    border-bottom: 4px solid var(--accent-tertiary);
    animation-duration: 1s;
}

.loading-text {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Simple Animations */
@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* HTML Preview Modal */
.preview-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-modal);
}

.preview-modal-content {
    background: var(--bg-primary);
    margin: 2% auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    width: 90%;
    height: 90%;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.preview-title {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.preview-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.preview-btn {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    font-size: 1rem;
}

.preview-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-hover);
}

.preview-body {
    flex: 1;
    overflow: hidden;
    position: relative;
}

#previewFrame {
    width: 100%;
    height: 100%;
    border: none;
    background-color: white;
    border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
}

.preview-modal.fullscreen {
    z-index: 1001;
}

.preview-modal.fullscreen .preview-modal-content {
    width: 100%;
    height: 100%;
    margin: 0;
    border-radius: 0;
}

.preview-modal.fullscreen .preview-header {
    border-radius: 0;
}

.preview-modal.fullscreen .preview-body,
.preview-modal.fullscreen #previewFrame {
    border-radius: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 100vw;
    }

    .sidebar {
        width: 100vw;
        max-width: 320px;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .message {
        margin: var(--spacing-md);
        max-width: 90%;
    }

    .input-section {
        padding: var(--spacing-md);
    }

    .preview-modal-content {
        width: 95%;
        height: 95%;
        margin: 2.5% auto;
    }
}

@media (max-width: 480px) {
    .welcome-title {
        font-size: 1.75rem;
    }

    .message {
        padding: var(--spacing-md);
        margin: var(--spacing-sm);
    }

    .message-avatar {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .send-btn {
        width: 32px;
        height: 32px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .input-section {
        display: none !important;
    }

    .main-content {
        margin: 0;
        padding: 0;
    }

    .message {
        break-inside: avoid;
        margin: 1rem 0;
        padding: 1rem;
        border: 1px solid #ccc;
        border-radius: 0.5rem;
    }
}